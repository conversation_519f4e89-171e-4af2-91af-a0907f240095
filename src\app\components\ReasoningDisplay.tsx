import { Message } from '../types';

type ReasoningDisplayProps = {
  reasoning: Message['reasoning'];
};

export default function ReasoningDisplay({ reasoning }: ReasoningDisplayProps) {
  if (!reasoning || reasoning.length === 0) return null;

  return (
    <div className="mt-4 border-t border-gray-700 pt-3">
      <div className="font-semibold mb-2 text-gray-400">Reasoning:</div>
      <div className="bg-gray-700 p-3 rounded">
        {reasoning.map((step, i) => (
          <div key={i} className="mb-3">
            <div className="font-medium text-gray-300">{step.title}</div>
            <div className="text-gray-400">{step.explanation}</div>
          </div>
        ))}
      </div>
    </div>
  );
}
