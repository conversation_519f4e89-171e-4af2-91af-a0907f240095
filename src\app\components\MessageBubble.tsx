import { Message } from '../types';

type MessageBubbleProps = {
  message: Message;
};

export default function MessageBubble({ message }: MessageBubbleProps) {
  return (
    <div className="mb-6 max-w-4xl mx-auto">
      <div
        className={`p-4 rounded-lg ${
          message.role === 'user'
            ? 'bg-blue-600 ml-auto max-w-2xl'
            : 'bg-gray-800 mr-auto'
        }`}
      >
        <div className="font-semibold mb-2 text-sm opacity-70">
          {message.role === 'user' ? 'You' : 'Assistant'}
        </div>
        <div className="whitespace-pre-wrap text-white leading-relaxed">{message.content}</div>
      </div>
    </div>
  );
}
