import { Message } from '../types';

type MessageBubbleProps = {
  message: Message;
};

export default function MessageBubble({ message }: MessageBubbleProps) {
  return (
    <div
      className={`mb-6 p-4 rounded-lg max-w-3xl ${
        message.role === 'user' 
          ? 'bg-blue-900 ml-auto' 
          : 'bg-gray-800 mr-auto'
      }`}
    >
      <div className="font-semibold mb-2 text-blue-300">
        {message.role === 'user' ? 'You' : 'Assistant'}
      </div>
      <div className="whitespace-pre-wrap">{message.content}</div>
    </div>
  );
}
