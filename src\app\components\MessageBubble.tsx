import { Message } from '../types';
import MessageActions from './MessageActions';

type MessageBubbleProps = {
  message: Message;
};

export default function MessageBubble({ message }: MessageBubbleProps) {
  if (message.role === 'user') {
    return (
      <div className="mb-6 max-w-4xl mx-auto">
        <div className="flex justify-end">
          <div className="bg-blue-600 p-4 rounded-lg max-w-2xl">
            <div className="whitespace-pre-wrap text-white leading-relaxed">{message.content}</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-6 max-w-4xl mx-auto group">
      <div className="flex items-start gap-3">
        {/* Assistant Avatar */}
        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
          <span className="text-white text-sm font-bold">🤖</span>
        </div>

        {/* Message Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-2">
            <span className="font-semibold text-white text-sm">Assistant</span>
            <span className="text-xs text-gray-400">Thought for 0.7 seconds</span>
          </div>

          <div className="whitespace-pre-wrap text-white leading-relaxed mb-2">
            {message.content}
          </div>

          {/* Action buttons */}
          <MessageActions messageId={message.id} content={message.content} />
        </div>
      </div>
    </div>
  );
}
