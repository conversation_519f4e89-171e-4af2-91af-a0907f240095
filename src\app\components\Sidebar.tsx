import React, { useState } from 'react';
import { Conversation } from '../types';
import SearchBar from './SearchBar';

type SidebarProps = {
  conversations: Conversation[];
  currentConversationId?: string;
  onConversationClick: (id: string) => void;
  onNewConversation: () => void;
};

export default function Sidebar({ conversations, currentConversationId, onConversationClick, onNewConversation }: SidebarProps) {
  const [searchQuery, setSearchQuery] = useState('');

  // Filter conversations based on search query
  const filteredConversations = conversations.filter(conv =>
    conv.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="w-64 bg-gray-900 border-r border-gray-700 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center gap-2 mb-4">
          <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-md flex items-center justify-center">
            <span className="text-white text-xs font-bold">T</span>
          </div>
          <span className="text-white font-semibold">together ai</span>
          <span className="text-xs text-gray-400 ml-auto">CHT</span>
        </div>

        <SearchBar
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          placeholder="Search chats"
        />
      </div>

      {/* New Chat Button */}
      <div className="p-4">
        <button
          onClick={onNewConversation}
          className="w-full p-3 border border-gray-600 rounded-lg hover:bg-gray-800 flex items-center justify-center text-gray-300 hover:text-white transition-colors"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          New Chat
        </button>
      </div>

      {/* Conversations List */}
      <div className="flex-1 overflow-y-auto px-4 pb-4">
        {filteredConversations.length === 0 ? (
          <div className="text-gray-500 text-sm text-center py-8">
            {searchQuery ? 'No chats found' : 'No conversations yet'}
          </div>
        ) : (
          <>
            <div className="text-gray-400 text-xs font-medium mb-3 px-2">Today</div>
            {filteredConversations.map(conv => (
              <div
                key={conv.id}
                className={`p-3 rounded-lg mb-2 cursor-pointer transition-colors ${
                  conv.id === currentConversationId
                    ? 'bg-gray-700 text-white'
                    : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                }`}
                onClick={() => onConversationClick(conv.id)}
              >
                <div className="truncate text-sm">{conv.title}</div>
              </div>
            ))}
          </>
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-700">
        <div className="flex items-center justify-between text-xs text-gray-400">
          <span>Hosted in North America</span>
          <div className="flex gap-2">
            <span>log in</span>
            <span>or</span>
            <span>sign up</span>
          </div>
        </div>
        <div className="text-xs text-gray-500 mt-1">
          Securely hosted in the US & Canada
        </div>
      </div>
    </div>
  );
}
