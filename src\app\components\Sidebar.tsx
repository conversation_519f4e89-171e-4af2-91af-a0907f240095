import { Conversation } from '../types';

type SidebarProps = {
  conversations: Conversation[];
  currentConversationId: string;
  onConversationClick: (id: string) => void;
  onNewConversation: () => void;
};

export default function Sidebar({ conversations, currentConversationId, onConversationClick, onNewConversation }: SidebarProps) {
  return (
    <div className="w-64 bg-gray-800 p-4 flex flex-col">
      <button 
        onClick={onNewConversation}
        className="mb-4 p-3 border border-gray-600 rounded-md hover:bg-gray-700 flex items-center"
      >
        <span className="mr-2">+</span> New Chat
      </button>
      
      <div className="flex-1 overflow-y-auto">
        {conversations.map(conv => (
          <div
            key={conv.id}
            className={`p-3 rounded-md mb-2 cursor-pointer ${
              conv.id === currentConversationId ? 'bg-gray-700' : 'hover:bg-gray-700'
            }`}
            onClick={() => onConversationClick(conv.id)}
          >
            {conv.title}
          </div>
        ))}
      </div>
    </div>
  );
}
