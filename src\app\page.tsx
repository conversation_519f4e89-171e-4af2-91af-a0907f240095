'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';
import Sidebar from './components/Sidebar';
import ModelSelector, { defaultModels } from './components/ModelSelector';
import { Conversation } from './types';

export default function Home() {
  const router = useRouter();
  const [input, setInput] = useState('');
  const [selectedModel, setSelectedModel] = useState('deepseek-r1');
  const [conversations, setConversations] = useState<Conversation[]>([
    {
      id: '1',
      title: 'How to build a React app',
      messages: []
    },
    {
      id: '2',
      title: 'Explain machine learning concepts',
      messages: []
    },
    {
      id: '3',
      title: 'Best practices for TypeScript',
      messages: []
    }
  ]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    // Generate a unique conversation ID
    const conversationId = Date.now().toString();
    // Redirect to the chat page
    router.push(`/chat/${conversationId}?query=${encodeURIComponent(input)}&model=${selectedModel}`);
  };

  const handleNewConversation = () => {
    const conversationId = Date.now().toString();
    router.push(`/chat/${conversationId}`);
  };

  const handleConversationClick = (id: string) => {
    router.push(`/chat/${id}`);
  };

  return (
    <div className="flex h-screen bg-gray-800">
      {/* Sidebar */}
      <Sidebar
        conversations={conversations}
        currentConversationId=""
        onConversationClick={handleConversationClick}
        onNewConversation={handleNewConversation}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Main Chat Area */}
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="max-w-2xl w-full text-center">
            <h1 className="text-3xl font-semibold text-white mb-8">What’s on your mind?</h1>

            {/* Model Selection */}
            <ModelSelector
              selectedModel={selectedModel}
              onModelChange={setSelectedModel}
              models={defaultModels}
            />

            {/* Input Form */}
            <form onSubmit={handleSubmit} className="relative">
              <div className="relative">
                <input
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Ask anything"
                  className="w-full px-4 py-4 pr-12 bg-gray-700 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
                />
                <button
                  type="submit"
                  disabled={!input.trim()}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 p-2 rounded-lg bg-white text-gray-900 hover:bg-gray-100 disabled:bg-gray-600 disabled:text-gray-400 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                </button>
              </div>
            </form>

            {/* Footer Text */}
            <div className="mt-8 text-sm text-gray-400">
              Built with Together AI APIs
              <span className="mx-2">•</span>
              <span className="text-blue-400 hover:text-blue-300 cursor-pointer">↗</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
