'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';
import Sidebar from './components/Sidebar';
import FrontpageInput from './components/FrontpageInput';
import { Conversation } from './types';

export default function Home() {
  const router = useRouter();
  const [input, setInput] = useState('');
  const [selectedModel, setSelectedModel] = useState('deepseek-r1');
  const [conversations, setConversations] = useState<Conversation[]>([
    {
      id: 'sample',
      title: 'Hello Message',
      messages: []
    }
  ]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const conversationId = Date.now().toString();
    router.push(`/chat/${conversationId}?query=${encodeURIComponent(input)}&model=${selectedModel}`);
  };

  const handleNewConversation = () => {
    const conversationId = Date.now().toString();
    router.push(`/chat/${conversationId}`);
  };

  const handleConversationClick = (id: string) => {
    router.push(`/chat/${id}`);
  };

  return (
    <div className="flex h-screen bg-gray-800">
      <Sidebar
        conversations={conversations}
        currentConversationId=""
        onConversationClick={handleConversationClick}
        onNewConversation={handleNewConversation}
      />
      <div className="flex-1 flex flex-col">
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="max-w-2xl w-full text-center">
            <h1 className="text-4xl font-semibold text-white mb-12">What's on your mind?</h1>
            <FrontpageInput
              selectedModel={selectedModel}
              onModelChange={setSelectedModel}
              input={input}
              onInputChange={setInput}
              onSubmit={handleSubmit}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
