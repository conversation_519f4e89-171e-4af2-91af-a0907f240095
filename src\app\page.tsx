'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function Home() {
  const router = useRouter();
  const [input, setInput] = useState('');
  const [selectedModel, setSelectedModel] = useState('deepseek-ai');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    // Generate a unique conversation ID
    const conversationId = Date.now().toString();
    // Redirect to the chat page
    router.push(`/chat/${conversationId}?query=${encodeURIComponent(input)}&model=${selectedModel}`);
  };

  return (
    <div className="flex h-screen bg-white text-gray-900">
      <div className="m-auto text-center">
        <h1 className="text-3xl font-bold mb-8">What’s on your mind?</h1>
        <form onSubmit={handleSubmit} className="w-full max-w-md mx-auto">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask anything."
            className="w-full p-4 border border-gray-300 rounded-lg focus:outline-none"
          />
          <button
            type="submit"
            className="mt-4 bg-blue-600 text-white p-4 rounded-lg"
          >
            Start Chat
          </button>
        </form>
      </div>
    </div>
  );
}
