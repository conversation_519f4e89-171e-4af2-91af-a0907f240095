import React from 'react';
import ModelSelector, { chatModels } from './ModelSelector';

type InputAreaProps = {
  input: string;
  isLoading: boolean;
  onInputChange: (value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  selectedModel?: string;
  onModelChange?: (model: string) => void;
  showModelSelector?: boolean;
};

export default function InputArea({
  input,
  isLoading,
  onInputChange,
  onSubmit,
  selectedModel = 'deepseek-r1',
  onModelChange = () => {},
  showModelSelector = false
}: InputAreaProps) {
  return (
    <div className="p-4 bg-gray-800 border-t border-gray-700">
      <div className="max-w-4xl mx-auto">
        {/* Model Selector for chat page */}
        {showModelSelector && (
          <div className="mb-3">
            <ModelSelector
              selectedModel={selectedModel}
              onModelChange={onModelChange}
              models={chatModels}
              variant="buttons"
            />
          </div>
        )}

        {/* Input Form */}
        <form onSubmit={onSubmit}>
          <div className="relative">
            <input
              type="text"
              value={input}
              onChange={(e) => onInputChange(e.target.value)}
              placeholder="Ask anything"
              className="w-full px-4 py-4 pr-12 bg-gray-700 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
              disabled={isLoading}
            />
            <button
              type="submit"
              disabled={isLoading || !input.trim()}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 p-2 rounded-lg bg-white text-gray-900 hover:bg-gray-100 disabled:bg-gray-600 disabled:text-gray-400 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            </button>
          </div>
        </form>

        {/* Footer text */}
        <div className="mt-3 text-center text-xs text-gray-400">
          Built with Together AI APIs
          <span className="mx-2">•</span>
          <span className="text-blue-400 hover:text-blue-300 cursor-pointer">↗</span>
        </div>
      </div>
    </div>
  );
}
