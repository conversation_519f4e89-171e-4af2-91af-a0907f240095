import React from 'react';

type InputAreaProps = {
  input: string;
  isLoading: boolean;
  onInputChange: (value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
};

export default function InputArea({ input, isLoading, onInputChange, onSubmit }: InputAreaProps) {
  return (
    <form onSubmit={onSubmit} className="p-4 bg-gray-800 border-t border-gray-700">
      <div className="flex">
        <input
          type="text"
          value={input}
          onChange={(e) => onInputChange(e.target.value)}
          placeholder="Type your message..."
          className="flex-1 p-3 rounded-l-lg bg-gray-700 text-white focus:outline-none"
          disabled={isLoading}
        />
        <button
          type="submit"
          className="bg-blue-600 text-white p-3 rounded-r-lg hover:bg-blue-700 disabled:bg-gray-600"
          disabled={isLoading || !input.trim()}
        >
          Send
        </button>
      </div>
    </form>
  );
}
