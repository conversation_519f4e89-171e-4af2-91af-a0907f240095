import { useState } from 'react';

type ChatHeaderProps = {
  selectedModel: string;
  onModelChange: (model: string) => void;
};

export default function ChatHeader({ selectedModel, onModelChange }: ChatHeaderProps) {
  return (
    <header className="bg-gray-800 p-4 flex justify-between items-center">
      <h1 className="text-xl font-bold">ChatGPT-like Interface</h1>
      <select 
        value={selectedModel}
        onChange={(e) => onModelChange(e.target.value)}
        className="bg-gray-700 text-white p-2 rounded"
      >
        <option value="gpt-4-turbo">GPT-4 Turbo</option>
        <option value="gpt-4">GPT-4</option>
        <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
      </select>
    </header>
  );
}
