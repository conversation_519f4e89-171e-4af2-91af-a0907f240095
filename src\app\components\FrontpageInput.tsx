import React, { useState } from 'react';

// Model options that match the screenshot
const frontpageModels = [
  {
    id: 'deepseek-r1',
    name: 'DeepSeek R1',
    icon: '🧠',
    description: 'Full R1, uses advanced reasoning'
  },
  {
    id: 'deepseek-v3',
    name: 'DeepSeek V3 (0324)',
    icon: '🧠',
    description: 'Powerful non-reasoning model'
  },
  {
    id: 'qwen-3-235b',
    name: 'Qwen 3 235B',
    icon: '🤖',
    description: 'Advanced model with hybrid reasoning'
  },
  {
    id: 'llama-4-maverick',
    name: 'Llama 4 Maverick',
    icon: '🦙',
    description: 'Long context SOTA multimodal model'
  },
  {
    id: 'llama-4-scout',
    name: 'Llama 4 Scout',
    icon: '🦙',
    description: 'Multimodal model, faster than Maverick'
  },
  {
    id: 'flux-dev',
    name: 'Flux Dev',
    icon: '⚠️',
    description: 'Great for most image generations'
  }
];

type FrontpageInputProps = {
  selectedModel: string;
  onModelChange: (model: string) => void;
  input: string;
  onInputChange: (value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
};

export default function FrontpageInput({
  selectedModel,
  onModelChange,
  input,
  onInputChange,
  onSubmit
}: FrontpageInputProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const selectedModelData = frontpageModels.find(m => m.id === selectedModel);

  const handleModelSelect = (modelId: string) => {
    onModelChange(modelId);
    setIsDropdownOpen(false);
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  return (
    <div className="relative max-w-3xl mx-auto">
      {/* Main Input Field */}
      <div className="relative bg-gray-700 border border-gray-600 rounded-xl overflow-hidden">
        <form onSubmit={onSubmit} className="flex items-center">
          <input
            type="text"
            value={input}
            onChange={(e) => onInputChange(e.target.value)}
            placeholder="Ask anything"
            className="flex-1 px-4 py-4 bg-transparent text-white placeholder-gray-400 focus:outline-none text-lg"
          />

          {/* Model selector buttons inside input */}
          <div className="flex items-center gap-1 px-2">
            <button
              type="button"
              onClick={toggleDropdown}
              className="flex items-center gap-1 px-2 py-1.5 bg-gray-600 hover:bg-gray-500 rounded-md transition-colors text-sm"
            >
              <span className="text-sm">🧠</span>
              <span className="text-white">DeepSeek R1</span>
              <svg className={`w-3 h-3 text-gray-300 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            <button
              type="button"
              className="flex items-center gap-1 px-2 py-1.5 bg-gray-600 hover:bg-gray-500 rounded-md transition-colors text-sm"
            >
              <span className="text-sm">🔍</span>
              <span className="text-white">Search:</span>
              <span className="text-xs bg-blue-600 text-white px-1.5 py-0.5 rounded">auto</span>
            </button>

            <button
              type="button"
              className="flex items-center gap-1 px-2 py-1.5 bg-gray-600 hover:bg-gray-500 rounded-md transition-colors text-sm"
            >
              <span className="text-sm">💭</span>
              <span className="text-white">Thinking:</span>
              <span className="text-xs bg-blue-600 text-white px-1.5 py-0.5 rounded">on</span>
            </button>
          </div>

          {/* Send button */}
          <div className="flex items-center gap-2 px-3">
            <button type="button" className="p-1 text-gray-400 hover:text-white transition-colors">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
              </svg>
            </button>
            <button
              type="submit"
              disabled={!input.trim()}
              className="p-1 text-gray-400 hover:text-white disabled:text-gray-600 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            </button>
          </div>
        </form>
      </div>

      {/* Dropdown */}
      {isDropdownOpen && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-gray-700 border border-gray-600 rounded-xl shadow-xl z-50">
          {frontpageModels.map((model) => (
            <button
              key={model.id}
              onClick={() => handleModelSelect(model.id)}
              className={`w-full flex items-center gap-3 px-4 py-4 text-left hover:bg-gray-600 transition-colors first:rounded-t-xl last:rounded-b-xl ${
                selectedModel === model.id ? 'bg-gray-600' : ''
              }`}
            >
              <span className="text-lg">{model.icon}</span>
              <div className="flex-1">
                <div className="font-medium text-white">{model.name}</div>
                <div className="text-sm text-gray-400">{model.description}</div>
              </div>
              {selectedModel === model.id && (
                <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
            </button>
          ))}
        </div>
      )}

      {/* Footer */}
      <div className="mt-8 text-center">
        <div className="flex items-center justify-center gap-2 text-sm text-gray-400">
          <span>🔗</span>
          <span>Built with</span>
          <a href="#" className="text-blue-400 hover:text-blue-300">Together AI APIs</a>
          <span>|</span>
          <a href="#" className="text-gray-400 hover:text-gray-300">𝕏</a>
          <a href="#" className="text-gray-400 hover:text-gray-300">in</a>
        </div>
      </div>
    </div>
  );
}
