import React, { useState } from 'react';

// Model options that match the screenshot
const frontpageModels = [
  {
    id: 'deepseek-r1',
    name: 'DeepSeek R1',
    icon: '🧠',
    description: 'Full R1, uses advanced reasoning'
  },
  {
    id: 'deepseek-v3',
    name: 'DeepSeek V3 (0324)',
    icon: '🧠',
    description: 'Powerful non-reasoning model'
  },
  {
    id: 'qwen-3-235b',
    name: 'Qwen 3 235B',
    icon: '🤖',
    description: 'Advanced model with hybrid reasoning'
  },
  {
    id: 'llama-4-maverick',
    name: 'Llama 4 Maverick',
    icon: '🦙',
    description: 'Long context SOTA multimodal model'
  },
  {
    id: 'llama-4-scout',
    name: 'Llama 4 Scout',
    icon: '🦙',
    description: 'Multimodal model, faster than Maverick'
  },
  {
    id: 'flux-dev',
    name: 'Flux Dev',
    icon: '⚠️',
    description: 'Great for most image generations'
  }
];

type FrontpageInputProps = {
  selectedModel: string;
  onModelChange: (model: string) => void;
  input: string;
  onInputChange: (value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
};

export default function FrontpageInput({
  selectedModel,
  onModelChange,
  input,
  onInputChange,
  onSubmit
}: FrontpageInputProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isThinkingOn, setIsThinkingOn] = useState(true);

  const selectedModelData = frontpageModels.find(m => m.id === selectedModel);

  const handleModelSelect = (modelId: string) => {
    onModelChange(modelId);
    setIsDropdownOpen(false);
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const toggleThinking = () => {
    setIsThinkingOn(!isThinkingOn);
  };

  return (
    <div className="relative max-w-2xl mx-auto">
      {/* Card Container */}
      <div className="bg-gray-700 border border-gray-600 rounded-xl p-4 space-y-4">

        {/* Top Section: Input + Submit Button */}
        <form onSubmit={onSubmit} className="flex gap-3">
          <input
            type="text"
            value={input}
            onChange={(e) => onInputChange(e.target.value)}
            placeholder="Ask anything"
            className="flex-1 px-4 py-3 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <button
            type="submit"
            disabled={!input.trim()}
            className="px-4 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:text-gray-400 text-white rounded-lg transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          </button>
        </form>

        {/* Bottom Section: Model Selector + Thinking Toggle */}
        <div className="flex items-center justify-between">
          {/* Model Selector Dropdown */}
          <div className="relative">
            <button
              type="button"
              onClick={toggleDropdown}
              className="flex items-center gap-2 px-3 py-2 bg-gray-600 hover:bg-gray-500 rounded-lg transition-colors"
            >
              <span className="text-lg">{selectedModelData?.icon}</span>
              <span className="text-white">{selectedModelData?.name}</span>
              <svg className={`w-4 h-4 text-gray-300 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>

          {/* Thinking Toggle */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-300">💭 Thinking:</span>
            <button
              type="button"
              onClick={toggleThinking}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                isThinkingOn ? 'bg-blue-600' : 'bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  isThinkingOn ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`text-sm ${isThinkingOn ? 'text-blue-400' : 'text-gray-400'}`}>
              {isThinkingOn ? 'on' : 'off'}
            </span>
          </div>
        </div>
      </div>

      {/* Model Dropdown */}
      {isDropdownOpen && (
        <div className="absolute top-full left-4 right-4 mt-2 bg-gray-600 border border-gray-500 rounded-lg shadow-xl z-50">
          {frontpageModels.map((model) => (
            <button
              key={model.id}
              onClick={() => handleModelSelect(model.id)}
              className={`w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-500 transition-colors first:rounded-t-lg last:rounded-b-lg ${
                selectedModel === model.id ? 'bg-gray-500' : ''
              }`}
            >
              <span className="text-lg">{model.icon}</span>
              <div className="flex-1">
                <div className="font-medium text-white">{model.name}</div>
                <div className="text-sm text-gray-300">{model.description}</div>
              </div>
              {selectedModel === model.id && (
                <svg className="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
            </button>
          ))}
        </div>
      )}

      {/* Footer */}
      <div className="mt-8 text-center">
        <div className="flex items-center justify-center gap-2 text-sm text-gray-400">
          <span>🔗</span>
          <span>Built with</span>
          <a href="#" className="text-blue-400 hover:text-blue-300">Together AI APIs</a>
          <span>|</span>
          <a href="#" className="text-gray-400 hover:text-gray-300">𝕏</a>
          <a href="#" className="text-gray-400 hover:text-gray-300">in</a>
        </div>
      </div>
    </div>
  );
}
