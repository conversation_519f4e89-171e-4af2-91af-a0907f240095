import React, { useState } from 'react';
import { frontpageModels } from './ModelSelector';

type FrontpageInputProps = {
  selectedModel: string;
  onModelChange: (model: string) => void;
  input: string;
  onInputChange: (value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
};

export default function FrontpageInput({ 
  selectedModel, 
  onModelChange, 
  input, 
  onInputChange, 
  onSubmit 
}: FrontpageInputProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  
  const selectedModelData = frontpageModels.find(m => m.id === selectedModel);

  const handleModelSelect = (modelId: string) => {
    onModelChange(modelId);
    setIsDropdownOpen(false);
  };

  const handleInputFocus = () => {
    setIsFocused(true);
    setIsDropdownOpen(true);
  };

  const handleInputBlur = () => {
    // Delay to allow clicking on dropdown items
    setTimeout(() => {
      setIsFocused(false);
      setIsDropdownOpen(false);
    }, 200);
  };

  return (
    <div className="relative">
      {/* Main Input Field */}
      <form onSubmit={onSubmit}>
        <div className="relative">
          <input
            type="text"
            value={input}
            onChange={(e) => onInputChange(e.target.value)}
            onFocus={handleInputFocus}
            onBlur={handleInputBlur}
            placeholder="Ask anything"
            className="w-full px-4 py-4 pr-12 bg-gray-700 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
          />
          
          {/* Model indicator on the left */}
          <div className="absolute left-4 top-1/2 transform -translate-y-1/2 flex items-center gap-2 pointer-events-none">
            {selectedModelData?.icon && (
              <span className="text-lg">{selectedModelData.icon}</span>
            )}
            <span className="text-sm text-gray-400">{selectedModelData?.name}</span>
          </div>
          
          {/* Send button */}
          <button
            type="submit"
            disabled={!input.trim()}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 p-2 rounded-lg bg-white text-gray-900 hover:bg-gray-100 disabled:bg-gray-600 disabled:text-gray-400 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          </button>
        </div>
      </form>

      {/* Dropdown */}
      {isDropdownOpen && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-gray-700 border border-gray-600 rounded-xl shadow-xl z-50">
          {frontpageModels.map((model) => (
            <button
              key={model.id}
              onClick={() => handleModelSelect(model.id)}
              className={`w-full flex items-center gap-3 px-4 py-4 text-left hover:bg-gray-600 transition-colors first:rounded-t-xl last:rounded-b-xl ${
                selectedModel === model.id ? 'bg-gray-600' : ''
              }`}
            >
              {model.icon && (
                <span className="text-lg">{model.icon}</span>
              )}
              <div className="flex-1">
                <div className="font-medium text-white">{model.name}</div>
                {model.description && (
                  <div className="text-sm text-gray-400">{model.description}</div>
                )}
              </div>
              {selectedModel === model.id && (
                <svg className="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
