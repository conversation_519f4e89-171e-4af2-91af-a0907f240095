import React, { useState } from 'react';

type ModelOption = {
  id: string;
  name: string;
  icon?: string;
  description?: string;
  badge?: string;
};

type ModelSelectorProps = {
  selectedModel: string;
  onModelChange: (model: string) => void;
  models: ModelOption[];
  variant?: 'dropdown' | 'buttons';
};

export default function ModelSelector({ selectedModel, onModelChange, models, variant = 'dropdown' }: ModelSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);

  const selectedModelData = models.find(m => m.id === selectedModel);

  if (variant === 'buttons') {
    return (
      <div className="flex flex-wrap gap-2 justify-center">
        {models.map((model) => (
          <button
            key={model.id}
            onClick={() => onModelChange(model.id)}
            className={`
              flex items-center gap-1.5 px-3 py-1.5 rounded-lg border text-xs transition-all duration-200
              ${selectedModel === model.id
                ? 'bg-white text-gray-900 border-white'
                : 'bg-transparent text-gray-300 border-gray-600 hover:border-gray-400 hover:text-white'
              }
            `}
          >
            {model.icon && (
              <span className="text-xs">{model.icon}</span>
            )}
            <span className="font-medium">{model.name}</span>
            {model.badge && (
              <span className="text-xs bg-blue-600 text-white px-1.5 py-0.5 rounded text-xs">{model.badge}</span>
            )}
          </button>
        ))}
      </div>
    );
  }

  return (
    <div className="relative mb-4">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full max-w-2xl mx-auto flex items-center justify-between px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white hover:bg-gray-600 transition-colors"
      >
        <div className="flex items-center gap-3">
          {selectedModelData?.icon && (
            <span className="text-lg">{selectedModelData.icon}</span>
          )}
          <div className="text-left">
            <div className="font-medium">{selectedModelData?.name}</div>
            {selectedModelData?.description && (
              <div className="text-sm text-gray-400">{selectedModelData.description}</div>
            )}
          </div>
        </div>
        <svg className={`w-5 h-5 transition-transform ${isOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-gray-700 border border-gray-600 rounded-xl shadow-xl z-50 max-w-2xl mx-auto">
          {models.map((model) => (
            <button
              key={model.id}
              onClick={() => {
                onModelChange(model.id);
                setIsOpen(false);
              }}
              className={`w-full flex items-center gap-3 px-4 py-4 text-left hover:bg-gray-600 transition-colors first:rounded-t-xl last:rounded-b-xl ${
                selectedModel === model.id ? 'bg-gray-600' : ''
              }`}
            >
              {model.icon && (
                <span className="text-lg">{model.icon}</span>
              )}
              <div className="flex-1">
                <div className="font-medium text-white">{model.name}</div>
                {model.description && (
                  <div className="text-sm text-gray-400">{model.description}</div>
                )}
              </div>
              {selectedModel === model.id && (
                <svg className="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}

// Model options that match the first screenshot
export const frontpageModels: ModelOption[] = [
  {
    id: 'deepseek-r1',
    name: 'DeepSeek R1',
    icon: '🧠',
    description: 'Fast AI chat, advanced reasoning'
  },
  {
    id: 'deepseek-v3',
    name: 'DeepSeek V3 (0324)',
    icon: '🧠',
    description: 'Powerful text reasoning model'
  },
  {
    id: 'qwen-3-235b',
    name: 'Qwen 3 235B',
    icon: '🤖',
    description: 'Advanced model with hybrid reasoning'
  },
  {
    id: 'llama-4-maverick',
    name: 'Llama 4 Maverick',
    icon: '🦙',
    description: 'Long context SOTA multimodal model'
  },
  {
    id: 'llama-4-scout',
    name: 'Llama 4 Scout',
    icon: '🦙',
    description: 'Ultimate speed, faster than Maverick'
  },
  {
    id: 'flux-dev',
    name: 'Flux Dev',
    icon: '⚠️',
    description: 'Great for most image generation'
  }
];

// Model options for chat page (buttons)
export const chatModels: ModelOption[] = [
  {
    id: 'deepseek-r1',
    name: 'DeepSeek R1',
    icon: '🧠',
  },
  {
    id: 'search',
    name: 'Search',
    icon: '🔍',
    badge: 'PRO'
  },
  {
    id: 'thinking',
    name: 'Thinking',
    icon: '💭',
    badge: 'R1'
  },
];
