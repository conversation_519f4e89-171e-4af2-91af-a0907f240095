import React from 'react';

type ModelOption = {
  id: string;
  name: string;
  icon?: string;
  description?: string;
};

type ModelSelectorProps = {
  selectedModel: string;
  onModelChange: (model: string) => void;
  models: ModelOption[];
};

export default function ModelSelector({ selectedModel, onModelChange, models }: ModelSelectorProps) {
  return (
    <div className="flex flex-wrap gap-3 mb-6">
      {models.map((model) => (
        <button
          key={model.id}
          onClick={() => onModelChange(model.id)}
          className={`
            flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-200
            ${selectedModel === model.id 
              ? 'bg-white text-gray-900 border-white' 
              : 'bg-transparent text-gray-300 border-gray-600 hover:border-gray-400 hover:text-white'
            }
          `}
        >
          {model.icon && (
            <span className="text-sm">{model.icon}</span>
          )}
          <span className="text-sm font-medium">{model.name}</span>
        </button>
      ))}
    </div>
  );
}

// Default model options that match the screenshot
export const defaultModels: ModelOption[] = [
  {
    id: 'deepseek-r1',
    name: 'DeepSeek R1',
    icon: '🧠',
  },
  {
    id: 'search',
    name: 'Search',
    icon: '🔍',
  },
  {
    id: 'thinking',
    name: 'Thinking',
    icon: '💭',
  },
];
