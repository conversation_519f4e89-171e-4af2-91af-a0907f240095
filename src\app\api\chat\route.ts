import { OpenAI } from 'openai';

// Create an OpenAI API client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export const runtime = 'edge';

export async function POST(req: Request) {
  const { messages } = await req.json();

  // Ask OpenAI for a streaming chat completion given the prompt
  const response = await openai.chat.completions.create({
    model: 'gpt-4-turbo',
    stream: true,
    messages,
    functions: [
      {
        name: 'show_reasoning',
        description: 'Display step-by-step reasoning for the solution',
        parameters: {
          type: 'object',
          properties: {
            steps: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  title: { type: 'string' },
                  explanation: { type: 'string' },
                },
                required: ['title', 'explanation'],
              },
            },
          },
          required: ['steps'],
        },
      },
    ],
  });

  let fullContent = '';
  let reasoning: Array<{ title: string; explanation: string }> | null = null;

  for await (const chunk of response) {
    const content = chunk.choices[0]?.delta?.content || '';
    fullContent += content;

    // Check for function call
    if (chunk.choices[0]?.delta?.function_call) {
      const functionCall = chunk.choices[0].delta.function_call;
      if (functionCall.name) {
        // Start of function call
        reasoning = [];
      } else if (functionCall.arguments) {
        // Accumulate function call arguments
        try {
          const args = JSON.parse(functionCall.arguments);
          if (args.steps) {
            reasoning = args.steps;
          }
        } catch (e) {
          console.error('Error parsing function arguments', e);
        }
      }
    }
  }

  // Return both content and reasoning
  return new Response(JSON.stringify({
    content: fullContent,
    reasoning: reasoning || undefined,
  }), {
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
