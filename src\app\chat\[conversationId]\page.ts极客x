'use client';

import { useParams, useSearchParams } from 'next/navigation';
import { useState, useRef, useEffect } from 'react';
import { Message, Conversation } from '@/app/types';
import ChatHeader from '@/app/components/ChatHeader';
import MessageBubble from '@/app/components/MessageBubble';
import ReasoningDisplay from '@/app/components/ReasoningDisplay';
import InputArea from '@/app/components/InputArea';

export default function ChatPage() {
  const { conversationId } = useParams();
  const searchParams = useSearchParams();
  const initialQuery = searchParams.get('query') || '';
  const initialModel = searchParams.get('model') || 'gpt-4-turbo';
  const [conversation, setConversation] = useState<Conversation | null>(null);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedModel, setSelectedModel] = useState(initialModel);
  const messagesEndRef = useRef<null | HTMLDivElement>(null);

  // In a real app, you would fetch the conversation from a data source using the conversationId
  // For now, we'll initialize a new conversation if none exists
  useEffect(() => {
    if (!conversation) {
      const newConversation: Conversation = {
        id: conversationId as string,
        title: `Conversation ${conversationId}`,
        messages: [],
      };
      setConversation(newConversation);

      // If there's an initial query, send it immediately
      if (initialQuery) {
        setInput(initialQuery);
        handleSubmit(newConversation, initialQuery);
      }
    }
  }, [conversation, conversationId, initialQuery]);

  const handleSubmit = async (conversation: Conversation, inputText: string) => {
    if (!inputText.trim() || isLoading) return;

    setIsLoading(true);
    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: inputText,
    };

    const updatedMessages = [...conversation.messages, userMessage];
    setConversation({
      ...conversation,
      messages: updatedMessages,
    });
    setInput('');

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: selectedModel,
          messages: [
            ...conversation.messages,
            { role: 'user', content: inputText },
          ],
        }),
      });

      if (!response.ok) throw new Error('Network response was not ok');
      
      const data = await response.json();
      
      const assistantMessage: Message = {
        id: Date.now().toString(),
        role: 'assistant',
        content: data.content,
        reasoning: data.reasoning,
      };
      
      setConversation({
        ...conversation,
        messages: [...updatedMessages, assistantMessage],
      });
    } catch (error) {
      console.error('Error:', error);
      setConversation({
        ...conversation,
        messages: [
          ...updatedMessages,
          {
            id: Date.now().toString(),
            role: 'assistant',
            content: 'Sorry, something went wrong.',
          }
        ],
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (conversation) {
      handleSubmit(conversation, input);
    }
  };

  if (!conversation) {
    return <div>Loading...</div>;
  }

  const messages = conversation.messages;

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <div className="flex flex-col h-screen bg-gray-900 text-gray-100">
      <ChatHeader 
        selectedModel={selectedModel}
        onModelChange={setSelectedModel}
      />

      <div className="flex-1 overflow-y-auto p-4 bg-gray-900">
        {messages.map((message) => (
          <div key={message.id}>
            <MessageBubble message={message} />
            <ReasoningDisplay reasoning={message.reasoning} />
          </div>
        ))}
        {isLoading && (
          <div className="mb-6 p-4 rounded-lg bg-gray-800 max-w-3xl mr-auto">
            <div className="font-semibold mb-1">Assistant</div>
            <div className="flex space-x-2">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.4s' }}></div>
            </极客div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      <InputArea 
        input={input}
        isLoading={isLoading}
        onInputChange={setInput}
        onSubmit={handleFormSubmit}
      />
    </div>
  );
}
