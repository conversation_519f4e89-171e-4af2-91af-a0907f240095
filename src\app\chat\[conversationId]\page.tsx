'use client';

import { use<PERSON>ara<PERSON>, useRouter, useSearchParams } from 'next/navigation';
import { useState, useRef, useEffect } from 'react';
import { Message, Conversation } from '@/app/types';
import Sidebar from '@/app/components/Sidebar';
import MessageBubble from '@/app/components/MessageBubble';
import ReasoningDisplay from '@/app/components/ReasoningDisplay';
import InputArea from '@/app/components/InputArea';

export default function ChatPage() {
  const { conversationId } = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [conversation, setConversation] = useState<Conversation | null>(null);
  const [conversations, setConversations] = useState<Conversation[]>([
    {
      id: 'sample',
      title: 'Hello Message',
      messages: []
    },
    {
      id: '1',
      title: 'How to build a React app',
      messages: []
    },
    {
      id: '2',
      title: 'Explain machine learning concepts',
      messages: []
    },
    {
      id: '3',
      title: 'Best practices for TypeScript',
      messages: []
    }
  ]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedModel, setSelectedModel] = useState('gpt-4-turbo');
  const messagesEndRef = useRef<null | HTMLDivElement>(null);

  // In a real app, you would fetch the conversation from a data source using the conversationId
  // For now, we'll initialize a new conversation if none exists
  useEffect(() => {
    if (!conversation) {
      const initialQuery = searchParams.get('query');
      const initialModel = searchParams.get('model');

      if (initialModel) {
        setSelectedModel(initialModel);
      }

      const sampleMessages = conversationId === 'sample' ? [
        {
          id: '1',
          role: 'user' as const,
          content: 'hello'
        },
        {
          id: '2',
          role: 'assistant' as const,
          content: "Hello! 👋 I'm Together Chat, your AI assistant by Together AI. How can I help you today? Whether you have questions about math, coding, diagrams, or anything else—just let me know!"
        }
      ] : [];

      setConversation({
        id: conversationId as string,
        title: initialQuery ? initialQuery.slice(0, 50) + '...' : conversationId === 'sample' ? 'Hello Message' : `Conversation ${conversationId}`,
        messages: sampleMessages,
      });

      // If there's an initial query, set it as input
      if (initialQuery) {
        setInput(initialQuery);
      }
    }
  }, [conversation, conversationId, searchParams]);

  const messages = conversation?.messages || [];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading || !conversation) return;

    setIsLoading(true);
    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input,
    };

    const updatedMessages = [...messages, userMessage];
    setConversation({
      ...conversation,
      messages: updatedMessages,
    });
    setInput('');

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: selectedModel,
          messages: [
            ...messages,
            { role: 'user', content: input },
          ],
        }),
      });

      if (!response.ok) throw new Error('Network response was not ok');

      const data = await response.json();

      const assistantMessage: Message = {
        id: Date.now().toString(),
        role: 'assistant',
        content: data.content,
        reasoning: data.reasoning,
      };

      setConversation({
        ...conversation,
        messages: [...updatedMessages, assistantMessage],
      });
    } catch (error) {
      console.error('Error:', error);
      setConversation({
        ...conversation,
        messages: [
          ...updatedMessages,
          {
            id: Date.now().toString(),
            role: 'assistant',
            content: 'Sorry, something went wrong.',
          }
        ],
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewConversation = () => {
    const newConversationId = Date.now().toString();
    router.push(`/chat/${newConversationId}`);
  };

  const handleConversationClick = (id: string) => {
    router.push(`/chat/${id}`);
  };

  if (!conversation) {
    return <div>Loading...</div>;
  }

  return (
    <div className="flex h-screen bg-gray-800">
      {/* Sidebar */}
      <Sidebar
        conversations={conversations}
        currentConversationId={conversationId as string}
        onConversationClick={handleConversationClick}
        onNewConversation={handleNewConversation}
      />

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col bg-gray-900 text-gray-100">
        <div className="flex-1 overflow-y-auto p-4">
          {messages.map((message) => (
            <div key={message.id}>
              <MessageBubble message={message} />
              <ReasoningDisplay reasoning={message.reasoning} />
            </div>
          ))}
          {isLoading && (
            <div className="mb-6 p-4 rounded-lg bg-gray-800 max-w-3xl mr-auto">
              <div className="font-semibold mb-1">Assistant</div>
              <div className="flex space-x-2">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.4s' }}></div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>

        <InputArea
          input={input}
          isLoading={isLoading}
          onInputChange={setInput}
          onSubmit={handleSubmit}
          selectedModel={selectedModel}
          onModelChange={setSelectedModel}
          showModelSelector={true}
        />
      </div>
    </div>
  );
}
